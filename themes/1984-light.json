{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984 Light", "author": "converted from VS Code theme", "themes": [{"name": "1984 Light", "appearance": "light", "style": {"border": "#a8b6ca", "border.variant": "#a8b6ca", "border.focused": "#46BDFF", "border.selected": "#46BDFF", "border.transparent": "#a8b6ca", "border.disabled": "#a8b6ca", "elevated_surface.background": "#dfe0f0", "surface.background": "#dfe0f0", "background": "#e4e5f5", "element.background": "#46beff6b", "element.hover": "#46beff3f", "element.active": "#46beff6b", "element.selected": "#46beff6b", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#46beff3f", "ghost_element.active": null, "ghost_element.selected": "#46beff6b", "ghost_element.disabled": null, "text": "#19152c", "text.muted": "#585d74", "text.placeholder": "#7f8a99", "text.disabled": null, "text.accent": "#46BDFF", "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": "#46BDFF", "status_bar.background": "#070825", "title_bar.background": "#e4e5f5", "toolbar.background": "#e4e5f5", "tab_bar.background": "#dfe0f0", "tab.inactive_background": "#dfe0f0", "tab.active_background": "#e4e5f5", "search.match_background": "#ffffff", "panel.background": "#dfe0f0", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#4E566680", "scrollbar.thumb.hover_background": "#5A637580", "scrollbar.thumb.border": null, "scrollbar.track.background": "#e4e5f5", "scrollbar.track.border": null, "editor.foreground": "#19152c", "editor.background": "#e4e5f5", "editor.gutter.background": "#e4e5f5", "editor.subheader.background": null, "editor.active_line.background": "#dcddec", "editor.highlighted_line.background": null, "editor.line_number": "#7f8a99", "editor.active_line_number": "#19152c", "editor.invisible": "#f1f1f1", "editor.wrap_guide": "#f1f1f1", "editor.active_wrap_guide": "#f1f1f1", "editor.document_highlight.read_background": null, "editor.document_highlight.write_background": null, "terminal.background": "#e4e5f5", "terminal.foreground": "#19152c", "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#19152c", "terminal.ansi.bright_black": "#585d74", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#FF407B", "terminal.ansi.bright_red": "#FF407B", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#00af4f", "terminal.ansi.bright_green": "#00af4f", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#FF8D01", "terminal.ansi.bright_yellow": "#FF8D01", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#0098fd", "terminal.ansi.bright_blue": "#0098fd", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#FF16B0", "terminal.ansi.bright_magenta": "#FF16B0", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#ffffff", "terminal.ansi.bright_white": "#ffffff", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#FF407B", "conflict.background": null, "conflict.border": null, "created": "#0098fd", "created.background": null, "created.border": null, "deleted": "#FF16B0", "deleted.background": null, "deleted.border": null, "error": "#FF407B", "error.background": null, "error.border": null, "hidden": "#adadad", "hidden.background": null, "hidden.border": null, "hint": "#0098fd", "hint.background": null, "hint.border": null, "ignored": "#adadad", "ignored.background": null, "ignored.border": null, "info": "#0098fd", "info.background": null, "info.border": null, "modified": "#FF8D01", "modified.background": null, "modified.border": null, "predictive": "#585d74", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": "#00af4f", "success.background": null, "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": "#FF8D01", "warning.background": null, "warning.border": null, "players": [{"cursor": "#FF16B0ff", "background": "#FF16B0ff", "selection": "#FF16B03d"}, {"cursor": "#0098fdff", "background": "#0098fdff", "selection": "#0098fd3d"}, {"cursor": "#4d5effff", "background": "#4d5effff", "selection": "#4d5eff3d"}, {"cursor": "#c300ffff", "background": "#c300ffff", "selection": "#c300ff3d"}, {"cursor": "#FF8D01ff", "background": "#FF8D01ff", "selection": "#FF8D013d"}, {"cursor": "#00af4fff", "background": "#00af4fff", "selection": "#00af4f3d"}, {"cursor": "#1930fdff", "background": "#1930fdff", "selection": "#1930fd3d"}, {"cursor": "#9793b9ff", "background": "#9793b9ff", "selection": "#9793b93d"}], "syntax": {"comment": {"color": "#9793b9", "font_style": "italic", "font_weight": null}, "comment.doc": {"color": "#9793b9", "font_style": "italic", "font_weight": null}, "string": {"color": "#c300ff", "font_style": null, "font_weight": null}, "string.escape": {"color": "#19152c", "font_style": null, "font_weight": null}, "string.regex": {"color": "#4d5eff", "font_style": null, "font_weight": null}, "string.special": {"color": "#19152c", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#19152c", "font_style": null, "font_weight": null}, "variable": {"color": "#4d5eff", "font_style": null, "font_weight": null}, "variable.special": {"color": "#4d5eff", "font_style": null, "font_weight": null}, "constant": {"color": "#4d5eff", "font_style": null, "font_weight": null}, "number": {"color": "#c300ff", "font_style": null, "font_weight": null}, "boolean": {"color": "#c300ff", "font_style": null, "font_weight": null}, "type": {"color": "#0098fd", "font_style": null, "font_weight": null}, "function": {"color": "#00af4f", "font_style": null, "font_weight": null}, "keyword": {"color": "#FF16B0", "font_style": null, "font_weight": null}, "operator": {"color": "#c300ff", "font_style": null, "font_weight": null}, "punctuation": {"color": "#19152c", "font_style": null, "font_weight": null}, "tag": {"color": "#0098fd", "font_style": null, "font_weight": null}, "attribute": {"color": "#4d5eff", "font_style": null, "font_weight": null}, "constructor": {"color": "#0098fd", "font_style": null, "font_weight": null}, "embedded": {"color": "#19152c", "font_style": null, "font_weight": null}, "link_text": {"color": "#19152c", "font_style": null, "font_weight": null}, "link_uri": {"color": "#00af4f", "font_style": null, "font_weight": null}, "title": {"color": "#00af4f", "font_style": null, "font_weight": null}, "text.literal": {"color": "#c300ff", "font_style": null, "font_weight": null}, "property": {"color": "#0098fd", "font_style": null, "font_weight": null}}}}]}