{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984 Fancy", "author": "converted from VS Code theme", "themes": [{"name": "1984 Fancy", "appearance": "dark", "style": {"border": "#181A1F", "border.variant": "#181A1F", "border.focused": "#46BDFF", "border.selected": "#46BDFF", "border.transparent": "#181A1F", "border.disabled": "#181A1F", "elevated_surface.background": "#070825", "surface.background": "#070825", "background": "#0d0f31", "element.background": "#2C313A", "element.hover": "#2C313A", "element.active": "#2C313A", "element.selected": "#2C313A", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#2C313A", "ghost_element.active": null, "ghost_element.selected": "#2C313A", "ghost_element.disabled": null, "text": "#f1f1f1", "text.muted": "#b5becf", "text.placeholder": "#7b7f86", "text.disabled": null, "text.accent": "#46BDFF", "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": "#46BDFF", "status_bar.background": "#070825", "title_bar.background": "#070825", "toolbar.background": "#0d0f31", "tab_bar.background": "#070825", "tab.inactive_background": "#070825", "tab.active_background": "#0d0f31", "search.match_background": "#ffffff3b", "panel.background": "#070825", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#4E566680", "scrollbar.thumb.hover_background": "#5A637580", "scrollbar.thumb.border": null, "scrollbar.track.background": "#0d0f31", "scrollbar.track.border": null, "editor.foreground": "#f1f1f1", "editor.background": "#0d0f31", "editor.gutter.background": "#0d0f31", "editor.subheader.background": null, "editor.active_line.background": "#0f1a3121", "editor.highlighted_line.background": null, "editor.line_number": "#3B4D66", "editor.active_line_number": "#f1f1f1", "editor.invisible": "#2c394c8f", "editor.wrap_guide": "#2c394c8f", "editor.active_wrap_guide": "#2c394c8f", "editor.document_highlight.read_background": null, "editor.document_highlight.write_background": null, "terminal.background": "#0d0f31", "terminal.foreground": "#f1f1f1", "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#070825", "terminal.ansi.bright_black": "#4E5666", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#FF407B", "terminal.ansi.bright_red": "#FF407B", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#B3F361", "terminal.ansi.bright_green": "#B3F361", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#FFEA16", "terminal.ansi.bright_yellow": "#FFEA16", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#46BDFF", "terminal.ansi.bright_blue": "#46BDFF", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#FF16B0", "terminal.ansi.bright_magenta": "#FF16B0", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#f1f1f1", "terminal.ansi.bright_white": "#ffffff", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#FF407B", "conflict.background": null, "conflict.border": null, "created": "#46BDFF", "created.background": "#00809B33", "created.border": null, "deleted": "#FF16B0", "deleted.background": null, "deleted.border": null, "error": "#FF407B", "error.background": null, "error.border": null, "hidden": "#7b7f86", "hidden.background": null, "hidden.border": null, "hint": "#46BDFF", "hint.background": null, "hint.border": null, "ignored": "#D5D8DA59", "ignored.background": null, "ignored.border": null, "info": "#46BDFF", "info.background": null, "info.border": null, "modified": "#fcee54", "modified.background": null, "modified.border": null, "predictive": "#b5becf", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": "#B3F361", "success.background": null, "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": "#FFEA16", "warning.background": null, "warning.border": null, "players": [{"cursor": "#B3F361ff", "background": "#B3F361ff", "selection": "#B3F3613d"}, {"cursor": "#46BDFFff", "background": "#46BDFFff", "selection": "#46BDFF3d"}, {"cursor": "#FF16B0ff", "background": "#FF16B0ff", "selection": "#FF16B03d"}, {"cursor": "#FF407Bff", "background": "#FF407Bff", "selection": "#FF407B3d"}, {"cursor": "#FFEA16ff", "background": "#FFEA16ff", "selection": "#FFEA163d"}, {"cursor": "#59E1E3ff", "background": "#59E1E3ff", "selection": "#59E1E33d"}, {"cursor": "#6BE4E6ff", "background": "#6BE4E6ff", "selection": "#6BE4E63d"}, {"cursor": "#96A1FFff", "background": "#96A1FFff", "selection": "#96A1FF3d"}], "syntax": {"comment": {"color": "#525863", "font_style": "italic", "font_weight": null}, "comment.doc": {"color": "#525863", "font_style": "italic", "font_weight": null}, "string": {"color": "#DF81FC", "font_style": null, "font_weight": null}, "string.escape": {"color": "#fcfcfc", "font_style": null, "font_weight": null}, "string.regex": {"color": "#96A1FF", "font_style": null, "font_weight": null}, "string.special": {"color": "#fcfcfc", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#fcfcfc", "font_style": null, "font_weight": null}, "variable": {"color": "#96A1FF", "font_style": null, "font_weight": null}, "variable.special": {"color": "#46BDFF", "font_style": null, "font_weight": null}, "constant": {"color": "#96A1FF", "font_style": null, "font_weight": null}, "number": {"color": "#DF81FC", "font_style": null, "font_weight": null}, "boolean": {"color": "#DF81FC", "font_style": null, "font_weight": null}, "type": {"color": "#46BDFF", "font_style": null, "font_weight": null}, "function": {"color": "#B3F361", "font_style": null, "font_weight": null}, "keyword": {"color": "#FF16B0", "font_style": "italic", "font_weight": null}, "operator": {"color": "#DF81FC", "font_style": null, "font_weight": null}, "punctuation": {"color": "#bcd4cf", "font_style": null, "font_weight": null}, "tag": {"color": "#46BDFF", "font_style": null, "font_weight": null}, "attribute": {"color": "#BCD4CF", "font_style": null, "font_weight": null}, "constructor": {"color": "#46BDFF", "font_style": null, "font_weight": null}, "embedded": {"color": "#fcfcfc", "font_style": null, "font_weight": null}, "link_text": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "link_uri": {"color": "#B3F361", "font_style": null, "font_weight": null}, "title": {"color": "#B3F361", "font_style": null, "font_weight": null}, "text.literal": {"color": "#DF81FC", "font_style": null, "font_weight": null}, "property": {"color": "#46BDFF", "font_style": null, "font_weight": null}}}}]}