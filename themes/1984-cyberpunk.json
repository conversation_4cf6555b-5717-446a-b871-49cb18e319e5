{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "1984 Cyberpunk", "author": "converted from VS Code theme", "themes": [{"name": "1984 Cyberpunk", "appearance": "dark", "style": {"border": "#181A1F", "border.variant": "#181A1F", "border.focused": "#85EEA7", "border.selected": "#85EEA7", "border.transparent": "#181A1F", "border.disabled": "#181A1F", "elevated_surface.background": "#1C1E27", "surface.background": "#1C1E27", "background": "#1C1E27", "element.background": "#2C313A", "element.hover": "#2C313A", "element.active": "#2C313A", "element.selected": "#2C313A", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#2C313A", "ghost_element.active": null, "ghost_element.selected": "#2C313A", "ghost_element.disabled": null, "text": "#f5f5f5", "text.muted": "#b5becf", "text.placeholder": "#7b7f86", "text.disabled": null, "text.accent": "#85EEA7", "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": "#85EEA7", "status_bar.background": "#1C1E27", "title_bar.background": "#1C1E27", "toolbar.background": "#1C1E27", "tab_bar.background": "#1C1E27", "tab.inactive_background": "#15171d", "tab.active_background": "#1C1E27", "search.match_background": "#ffffff3b", "panel.background": "#1C1E27", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#4E566680", "scrollbar.thumb.hover_background": "#5A637580", "scrollbar.thumb.border": null, "scrollbar.track.background": "#1C1E27", "scrollbar.track.border": null, "editor.foreground": "#f5f5f5", "editor.background": "#1C1E27", "editor.gutter.background": "#1C1E27", "editor.subheader.background": null, "editor.active_line.background": "#0f1a3121", "editor.highlighted_line.background": null, "editor.line_number": "#8f97ff4d", "editor.active_line_number": "#f5f5f5", "editor.invisible": "#8f97ff28", "editor.wrap_guide": "#8f97ff28", "editor.active_wrap_guide": "#8f97ff28", "editor.document_highlight.read_background": null, "editor.document_highlight.write_background": null, "terminal.background": "#1C1E27", "terminal.foreground": "#f1f1f1", "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#070930", "terminal.ansi.bright_black": "#4E5666", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#FF407B", "terminal.ansi.bright_red": "#FF407B", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#85EEA7", "terminal.ansi.bright_green": "#85EEA7", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#FFEA16", "terminal.ansi.bright_yellow": "#FFEA16", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#78A8D6", "terminal.ansi.bright_blue": "#78A8D6", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#F806FA", "terminal.ansi.bright_magenta": "#F806FA", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#59E1E3", "terminal.ansi.bright_cyan": "#6BE4E6", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#f1f1f1", "terminal.ansi.bright_white": "#ffffff", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#FF407B", "conflict.background": null, "conflict.border": null, "created": "#85EEA7", "created.background": "#00809B33", "created.border": null, "deleted": "#FF16B0", "deleted.background": null, "deleted.border": null, "error": "#FF407B", "error.background": null, "error.border": null, "hidden": "#7b7f86", "hidden.background": null, "hidden.border": null, "hint": "#78A8D6", "hint.background": null, "hint.border": null, "ignored": "#D5D8DA59", "ignored.background": null, "ignored.border": null, "info": "#78A8D6", "info.background": null, "info.border": null, "modified": "#fcee54", "modified.background": null, "modified.border": null, "predictive": "#b5becf", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": "#85EEA7", "success.background": null, "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": "#FFEA16", "warning.background": null, "warning.border": null, "players": [{"cursor": "#85EEA7ff", "background": "#85EEA7ff", "selection": "#85EEA73d"}, {"cursor": "#78A8D6ff", "background": "#78A8D6ff", "selection": "#78A8D63d"}, {"cursor": "#F806FAff", "background": "#F806FAff", "selection": "#F806FA3d"}, {"cursor": "#FF407Bff", "background": "#FF407Bff", "selection": "#FF407B3d"}, {"cursor": "#FFEA16ff", "background": "#FFEA16ff", "selection": "#FFEA163d"}, {"cursor": "#59E1E3ff", "background": "#59E1E3ff", "selection": "#59E1E33d"}, {"cursor": "#6BE4E6ff", "background": "#6BE4E6ff", "selection": "#6BE4E63d"}, {"cursor": "#fcee54ff", "background": "#fcee54ff", "selection": "#fcee543d"}], "syntax": {"comment": {"color": "#525863", "font_style": "italic", "font_weight": null}, "comment.doc": {"color": "#525863", "font_style": "italic", "font_weight": null}, "string": {"color": "#7ADAD1", "font_style": null, "font_weight": null}, "string.escape": {"color": "#f2f2f2", "font_style": null, "font_weight": null}, "string.regex": {"color": "#85EEA7", "font_style": null, "font_weight": null}, "string.special": {"color": "#f3f3f3", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#f3f3f3", "font_style": null, "font_weight": null}, "variable": {"color": "#85EEA7", "font_style": null, "font_weight": null}, "variable.special": {"color": "#78A8D6", "font_style": null, "font_weight": null}, "constant": {"color": "#85EEA7", "font_style": null, "font_weight": null}, "number": {"color": "#7ADAD1", "font_style": null, "font_weight": 600}, "boolean": {"color": "#7ADAD1", "font_style": null, "font_weight": null}, "type": {"color": "#78A8D6", "font_style": null, "font_weight": 600}, "function": {"color": "#85EEA7", "font_style": null, "font_weight": 600}, "keyword": {"color": "#7ADAD1", "font_style": null, "font_weight": 600}, "operator": {"color": "#7ADAD1", "font_style": null, "font_weight": 600}, "punctuation": {"color": "#bcd4cf", "font_style": null, "font_weight": null}, "tag": {"color": "#78A8D6", "font_style": null, "font_weight": 600}, "attribute": {"color": "#BCD4CF", "font_style": null, "font_weight": null}, "constructor": {"color": "#78A8D6", "font_style": null, "font_weight": 600}, "embedded": {"color": "#f2f2f2", "font_style": null, "font_weight": null}, "link_text": {"color": "#F2F2F2", "font_style": null, "font_weight": null}, "link_uri": {"color": "#85EEA7", "font_style": null, "font_weight": null}, "title": {"color": "#85EEA7", "font_style": null, "font_weight": null}, "text.literal": {"color": "#7ADAD1", "font_style": null, "font_weight": 600}, "property": {"color": "#78A8D6", "font_style": null, "font_weight": null}}}}]}